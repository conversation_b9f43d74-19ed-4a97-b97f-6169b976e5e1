import { useSearchCustomer } from '@/apis/customer/customer.api';
import {
    SearchCustomer,
    SearchCustomerResponse,
} from '@/apis/customer/customer.type';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import SelectBusinessTypeControl from '@/components/common/FormController/SelectBusinessTypeControl';
import SelectIndustryControl from '@/components/common/FormController/SelectIndustryControl';
import { LeadStatus as LeadStatusData } from '@/constants/sharedData/sharedData';
import { MRT_ColumnDef } from 'mantine-react-table';
import dynamic from 'next/dynamic';
import { useMemo, useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import {
    Button,
    Card,
    CardHeader,
    Col,
    Modal,
    ModalBody,
    ModalFooter,
    ModalHeader,
} from 'reactstrap';

const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<SearchCustomerResponse>,
        })),
    {
        ssr: false,
    },
);

interface AddCustomerModalProps {
    isOpen: boolean;
    toggle: () => void;
    onSuccess?: (selectedCustomers: SearchCustomerResponse[]) => void;
}

const AddCustomerModal = ({
    isOpen,
    toggle,
    onSuccess,
}: AddCustomerModalProps) => {
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedData, setSelectedData] = useState<SearchCustomerResponse[]>(
        [],
    );
    const methods = useForm<SearchCustomer>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
        },
    });
    const { control, setValue } = methods;
    const [
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'IndustryId',
            'BusinessType',
            'LeadStatus',
            'SalePerson',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });
    const { data, isLoading } = useSearchCustomer({
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });
    const { items: listCustomer = [], totalItems } = data ?? {};
    const formattedCustomer = listCustomer.map(
        (Customer: SearchCustomerResponse) => ({
            ...Customer,
            createdOn: Customer.createdOn
                ? new Date(
                      Customer.createdOn.substring(0, 10),
                  ).toLocaleDateString('vi-VN')
                : '',
        }),
    );

    const columns = useMemo<MRT_ColumnDef<SearchCustomerResponse>[]>(
        () => [
            {
                accessorKey: 'name',
                header: 'Tên khách hàng',
                enableHiding: false,
            },
            {
                accessorKey: 'businessTypeName',
                header: 'Loại hình',
            },
            {
                accessorKey: 'industryName',
                header: 'Lĩnh vực',
            },
            {
                accessorKey: 'leadStatus',
                header: 'Giai đoạn',
                Cell: ({ cell }) => {
                    const status = LeadStatusData.find(
                        (s) => s.value === cell.getValue(),
                    );
                    return (
                        <span
                            className='badge me-1'
                            style={{
                                backgroundColor: '#daf4f0',
                                color: '#2fbeab',
                                display: 'inline-block',
                                textAlign: 'center',
                                padding: '4px 8px',
                                fontSize: '12px',
                                fontWeight: 500,
                                borderRadius: '4px',
                            }}
                        >
                            {status?.label || ''}
                        </span>
                    );
                },
            },
        ],
        [],
    );

    const handleRowSelectionChange = (
        updater:
            | Record<string, boolean>
            | ((prev: Record<string, boolean>) => Record<string, boolean>),
    ) => {
        let selectedRows: Record<string, boolean>;
        if (typeof updater === 'function') {
            const currentSelection = selectedIds.reduce(
                (acc, id) => {
                    const index = formattedCustomer.findIndex(
                        (Customer: SearchCustomerResponse) =>
                            Customer.id === id,
                    );
                    if (index !== -1) {
                        acc[index] = true;
                    }
                    return acc;
                },
                {} as Record<string, boolean>,
            );
            selectedRows = updater(currentSelection);
        } else {
            selectedRows = updater;
        }
        const newSelectedIds = Object.keys(selectedRows)
            .filter((key) => selectedRows[key])
            .map((key) => formattedCustomer[parseInt(key)].id);
        setSelectedIds(newSelectedIds);
        const newSelectedData = formattedCustomer.filter((item) =>
            newSelectedIds.includes(item.id),
        );
        setSelectedData(newSelectedData);
    };

    const handleCancel = () => {
        setSelectedIds([]);
        setSelectedData([]);
        toggle();
    };

    const handleSave = () => {
        if (onSuccess && selectedData.length > 0) {
            onSuccess(selectedData);
        }
        setSelectedIds([]);
        setSelectedData([]);
        toggle();
    };

    return (
        <Modal isOpen={isOpen} toggle={toggle} size='xl'>
            <FormProvider {...methods}>
                <ModalHeader toggle={toggle}>Thêm khách hàng</ModalHeader>
                <ModalBody>
                    <Col lg={12}>
                        <Card>
                            <CardHeader>
                                <div className='d-flex flex-wrap align-items-center gap-2'>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên nhóm khách hàng...'
                                    />
                                </div>
                            </CardHeader>

                            <MantineTable
                                columns={
                                    columns as MRT_ColumnDef<SearchCustomerResponse>[]
                                }
                                data={formattedCustomer}
                                isLoading={isLoading}
                                totalItems={totalItems ?? 0}
                                onPageChange={(page: number) =>
                                    setValue('PageNumber', page)
                                }
                                onPageSizeChange={(size: number) => {
                                    setValue('PageSize', size);
                                    setValue('PageNumber', 1);
                                }}
                                tableProps={{
                                    enableRowSelection: true,
                                    enableMultiRowSelection: true,
                                    selectAllMode: 'page',
                                    mantineSelectAllCheckboxProps: {
                                        size: 'xs',
                                        color: 'customGreen',
                                        style: {
                                            cursor: 'pointer',
                                            visibility: 'visible',
                                            display: 'inline-flex',
                                        },
                                    },
                                    mantineSelectCheckboxProps: {
                                        size: 'xs',
                                        color: 'customGreen',
                                        style: {
                                            cursor: 'pointer',
                                            visibility: 'visible',
                                            display: 'inline-flex',
                                        },
                                    },
                                    state: {
                                        rowSelection: selectedIds.reduce(
                                            (acc, id) => {
                                                const index =
                                                    formattedCustomer.findIndex(
                                                        (
                                                            Customer: SearchCustomerResponse,
                                                        ) => Customer.id === id,
                                                    );
                                                if (index !== -1) {
                                                    acc[index] = true;
                                                }
                                                return acc;
                                            },
                                            {} as Record<string, boolean>,
                                        ),
                                    },
                                    onRowSelectionChange:
                                        handleRowSelectionChange,
                                    mantineTableHeadCellProps: {
                                        align: 'left',
                                    },
                                    mantineTableBodyCellProps: {
                                        align: 'left',
                                    },
                                }}
                            />
                        </Card>
                    </Col>
                </ModalBody>
                <ModalFooter>
                    <Button color='danger' onClick={handleCancel}>
                        Huỷ
                    </Button>
                    <Button
                        color='success'
                        onClick={handleSave}
                        disabled={selectedData.length === 0}
                    >
                        Lưu
                    </Button>
                </ModalFooter>
            </FormProvider>
        </Modal>
    );
};

export default AddCustomerModal;
